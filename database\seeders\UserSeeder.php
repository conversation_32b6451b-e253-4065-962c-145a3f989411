<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'phone' => '+1234567890',
            'address' => '123 Admin Street, Admin City, AC 12345',
            'email_verified_at' => now(),
        ]);

        // Create test customer users
        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'customer',
            'phone' => '+1234567891',
            'address' => '456 Customer Lane, Customer City, CC 67890',
            'email_verified_at' => now(),
        ]);

        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'customer',
            'phone' => '+1234567892',
            'address' => '789 Buyer Boulevard, Buyer City, BC 54321',
            'email_verified_at' => now(),
        ]);

        User::create([
            'name' => 'Mike Johnson',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'customer',
            'phone' => '+1234567893',
            'address' => '321 Shopper Street, Shopper City, SC 98765',
            'email_verified_at' => now(),
        ]);
    }
}
