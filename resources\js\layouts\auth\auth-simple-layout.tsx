import AppLogoIcon from '@/components/app-logo-icon';
import { Link } from '@inertiajs/react';
import { type PropsWithChildren } from 'react';

interface AuthLayoutProps {
    name?: string;
    title?: string;
    description?: string;
}

export default function AuthSimpleLayout({ children, title, description }: PropsWithChildren<AuthLayoutProps>) {
    return (
        <div className="flex min-h-svh flex-col items-center justify-center gap-6 bg-white p-6 md:p-10">
            <div className="w-full max-w-md">
                <div className="flex flex-col gap-8">
                    <div className="flex flex-col items-center gap-6">
                        <Link href={route('home')} className="flex flex-col items-center gap-3 font-medium group">
                            <div className="mb-1 flex h-12 w-12 items-center justify-center rounded-xl bg-orange-500 group-hover:bg-orange-600 transition-colors shadow-lg">
                                <AppLogoIcon className="size-8 fill-current text-white" />
                            </div>
                            <span className="text-3xl font-bold text-orange-500 tracking-tight">
                                CLICKTEE
                            </span>
                        </Link>

                        <div className="space-y-3 text-center">
                            <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
                            <p className="text-center text-sm text-gray-600 max-w-sm">{description}</p>
                        </div>
                    </div>

                    <div className="bg-white rounded-2xl border border-gray-200 shadow-xl p-8">
                        {children}
                    </div>
                </div>
            </div>
        </div>
    );
}
