import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { useCallback } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';

export default function Login({ status, canResetPassword }) {
    const { data, setData, post, processing, errors, reset } = useForm({
        email: '',
        password: '',
        remember: false,
    });

    const submit = useCallback((e) => {
        e.preventDefault();
        post(route('login'), {
            onFinish: () => reset('password'),
        });
    }, [post, reset]);

    return (
        <AuthLayout title="Welcome back" description="Sign in to your CLICKTEE account to continue shopping">
            <Head title="Log in" />

            {status && <div className="mb-6 text-center text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg p-3">{status}</div>}

            <form className="flex flex-col gap-6" onSubmit={submit}>
                <div className="grid gap-6">
                    <div className="grid gap-3">
                        <Label htmlFor="email" className="text-sm font-semibold text-gray-900">Email Address</Label>
                        <Input
                            id="email"
                            type="email"
                            required
                            autoFocus
                            tabIndex={1}
                            autoComplete="email"
                            value={data.email}
                            onChange={(e) => setData('email', e.target.value)}
                            placeholder="Enter your email"
                            className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-orange-500 focus:ring-orange-500/20 transition-colors"
                        />
                        <InputError message={errors.email} />
                    </div>

                    <div className="grid gap-3">
                        <div className="flex items-center justify-between">
                            <Label htmlFor="password" className="text-sm font-semibold text-gray-900">Password</Label>
                            {canResetPassword && (
                                <TextLink href={route('password.request')} className="text-sm text-orange-500 hover:text-orange-600 font-medium" tabIndex={5}>
                                    Forgot password?
                                </TextLink>
                            )}
                        </div>
                        <Input
                            id="password"
                            type="password"
                            required
                            tabIndex={2}
                            autoComplete="current-password"
                            value={data.password}
                            onChange={(e) => setData('password', e.target.value)}
                            placeholder="Enter your password"
                            className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-orange-500 focus:ring-orange-500/20 transition-colors"
                        />
                        <InputError message={errors.password} />
                    </div>

                    <div className="flex items-center space-x-3">
                        <Checkbox
                            id="remember"
                            name="remember"
                            checked={data.remember}
                            onClick={() => setData('remember', !data.remember)}
                            tabIndex={3}
                            className="border-2 border-gray-300 data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-500"
                        />
                        <Label htmlFor="remember" className="text-sm text-gray-700 font-medium">Remember me for 30 days</Label>
                    </div>

                    <Button
                        type="submit"
                        className="mt-4 w-full h-12 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                        tabIndex={4}
                        disabled={processing}
                    >
                        {processing && <LoaderCircle className="h-5 w-5 animate-spin mr-2" />}
                        {processing ? 'Signing in...' : 'Sign In'}
                    </Button>
                </div>

                <div className="text-center">
                    <p className="text-sm text-gray-600">
                        Don't have an account?{' '}
                        <TextLink href={route('register')} tabIndex={6} className="text-orange-500 hover:text-orange-600 font-semibold">
                            Create account
                        </TextLink>
                    </p>
                </div>
            </form>
        </AuthLayout>
    );
}
