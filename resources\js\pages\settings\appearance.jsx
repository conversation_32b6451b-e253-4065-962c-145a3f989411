import { Head } from '@inertiajs/react';

import AppearanceTabs from '@/components/appearance-tabs';
import HeadingSmall from '@/components/heading-small';
import { AppShell } from '@/components/app-shell';

export default function Appearance() {
    return (
        <AppShell>
            <Head title="Appearance settings" />

            <div className="container mx-auto px-4 py-8 max-w-4xl">
                {/* Page Header */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">Settings</h1>
                    <p className="text-gray-600">Manage your profile and account settings</p>
                </div>

                <div className="bg-white rounded-lg shadow-sm border p-6">
                    <div className="space-y-6">
                        <HeadingSmall title="Appearance settings" description="Update your account's appearance settings" />
                        <AppearanceTabs />
                    </div>
                </div>
            </div>
        </AppShell>
    );
}
