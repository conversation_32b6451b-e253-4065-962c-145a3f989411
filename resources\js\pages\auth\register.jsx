import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { useCallback } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';

export default function Register() {
    const { data, setData, post, processing, errors, reset } = useForm({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: 'customer',
        phone: '',
        address: '',
    });

    const submit = useCallback((e) => {
        e.preventDefault();
        post(route('register'), {
            onFinish: () => reset('password', 'password_confirmation'),
        });
    }, [post, reset]);

    return (
        <AuthLayout title="Join CLICKTEE" description="Create your account to start designing custom t-shirts">
            <Head title="Register" />
            <form className="flex flex-col gap-6" onSubmit={submit}>
                <div className="grid gap-6">
                    <div className="grid gap-3">
                        <Label htmlFor="name" className="text-sm font-semibold text-gray-900">Full Name</Label>
                        <Input
                            id="name"
                            type="text"
                            required
                            autoFocus
                            tabIndex={1}
                            autoComplete="name"
                            value={data.name}
                            onChange={(e) => setData('name', e.target.value)}
                            disabled={processing}
                            placeholder="Enter your full name"
                            className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-orange-500 focus:ring-orange-500/20 transition-colors"
                        />
                        <InputError message={errors.name} className="mt-2" />
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="email" className="text-sm font-semibold text-gray-900">Email Address</Label>
                        <Input
                            id="email"
                            type="email"
                            required
                            tabIndex={2}
                            autoComplete="email"
                            value={data.email}
                            onChange={(e) => setData('email', e.target.value)}
                            disabled={processing}
                            placeholder="Enter your email"
                            className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-orange-500 focus:ring-orange-500/20 transition-colors"
                        />
                        <InputError message={errors.email} />
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="password" className="text-sm font-semibold text-gray-900">Password</Label>
                        <Input
                            id="password"
                            type="password"
                            required
                            tabIndex={3}
                            autoComplete="new-password"
                            value={data.password}
                            onChange={(e) => setData('password', e.target.value)}
                            disabled={processing}
                            placeholder="Create a strong password"
                            className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-orange-500 focus:ring-orange-500/20 transition-colors"
                        />
                        <InputError message={errors.password} />
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="password_confirmation" className="text-sm font-semibold text-gray-900">Confirm Password</Label>
                        <Input
                            id="password_confirmation"
                            type="password"
                            required
                            tabIndex={4}
                            autoComplete="new-password"
                            value={data.password_confirmation}
                            onChange={(e) => setData('password_confirmation', e.target.value)}
                            disabled={processing}
                            placeholder="Confirm your password"
                            className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-orange-500 focus:ring-orange-500/20 transition-colors"
                        />
                        <InputError message={errors.password_confirmation} />
                    </div>



                    <div className="grid gap-3">
                        <Label htmlFor="phone" className="text-sm font-semibold text-gray-900">Phone Number <span className="text-gray-500 font-normal">(Optional)</span></Label>
                        <Input
                            id="phone"
                            type="tel"
                            tabIndex={5}
                            autoComplete="tel"
                            value={data.phone}
                            onChange={(e) => setData('phone', e.target.value)}
                            disabled={processing}
                            placeholder="Enter your phone number"
                            className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-orange-500 focus:ring-orange-500/20 transition-colors"
                        />
                        <InputError message={errors.phone} />
                    </div>

                    <div className="grid gap-3">
                        <Label htmlFor="address" className="text-sm font-semibold text-gray-900">Address <span className="text-gray-500 font-normal">(Optional)</span></Label>
                        <Input
                            id="address"
                            type="text"
                            tabIndex={6}
                            autoComplete="street-address"
                            value={data.address}
                            onChange={(e) => setData('address', e.target.value)}
                            disabled={processing}
                            placeholder="Enter your address"
                            className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-orange-500 focus:ring-orange-500/20 transition-colors"
                        />
                        <InputError message={errors.address} />
                    </div>

                    <Button
                        type="submit"
                        className="mt-6 w-full h-12 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                        tabIndex={7}
                        disabled={processing}
                    >
                        {processing && <LoaderCircle className="h-5 w-5 animate-spin mr-2" />}
                        {processing ? 'Creating account...' : 'Create Account'}
                    </Button>
                </div>

                <div className="text-center">
                    <p className="text-sm text-gray-600">
                        Already have an account?{' '}
                        <TextLink href={route('login')} tabIndex={8} className="text-orange-500 hover:text-orange-600 font-semibold">
                            Sign in
                        </TextLink>
                    </p>
                </div>
            </form>
        </AuthLayout>
    );
}
